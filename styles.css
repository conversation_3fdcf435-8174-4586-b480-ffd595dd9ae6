/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #1f2937;
    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
    padding: 32px 0;
    font-weight: 400;
    font-size: 14px;
}

/* Resume Container */
.resume-container {
    max-width: 8.5in;
    margin: 0 auto;
    background: white;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid #e5e7eb;
}

/* Page Layout */
.page {
    width: 8.5in;
    min-height: 11in;
    background: white;
    page-break-after: always;
    page-break-inside: avoid;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}

.page:last-child {
    page-break-after: avoid;
}

/* Header Styles */
.header {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
    padding: 32px 32px 24px 32px;
    background: #2d3748;
    color: white;
    position: relative;
    text-align: center;
}

.header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: #f56565;
}

.profile-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 24px;
}

.header-info {
    text-align: center;
}

.name {
    font-size: 28px;
    font-weight: 800;
    color: white;
    margin-bottom: 12px;
    letter-spacing: -0.5px;
}

.title {
    font-size: 15px;
    font-weight: 600;
    color: #f6ad55;
    text-transform: uppercase;
    letter-spacing: 1.2px;
}

.contact-info {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 12px;
    width: 100%;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: white;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 16px;
    border-radius: 8px;
}

.contact-item i {
    color: #f56565;
    font-size: 16px;
}

/* Contact Links */
.contact-link {
    text-decoration: none;
    color: inherit;
    transition: all 0.2s ease;
}

.contact-link:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.contact-link:visited {
    color: inherit;
}

.contact-link:active {
    transform: translateY(0);
}

/* Section Styles */
.section {
    margin-bottom: 20px;
    page-break-inside: avoid;
    padding: 0 32px;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 16px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 16px;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 8px;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: #f56565;
}

.section-title i {
    color: #f56565;
    font-size: 16px;
}

/* Professional Summary */
.summary-text {
    font-size: 14px;
    line-height: 1.6;
    color: #4a5568;
    text-align: justify;
    font-weight: 400;
    background: #fff5f5;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #f56565;
}

/* Experience Styles */
.experience-item {
    margin-bottom: 18px;
    padding: 16px;
    border-left: 4px solid #f56565;
    background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
    border-radius: 8px;
    page-break-inside: avoid;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.experience-header {
    margin-bottom: 10px;
}

.job-title {
    font-size: 15px;
    font-weight: 700;
    color: #111827;
    margin-bottom: 4px;
    letter-spacing: -0.25px;
}

.company {
    font-size: 13px;
    color: #0ea5e9;
    font-weight: 600;
    display: block;
    margin-bottom: 3px;
}

.duration {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
    font-style: italic;
}

.achievements {
    list-style: none;
    padding-left: 0;
    margin-top: 8px;
}

.achievements li {
    font-size: 13px;
    line-height: 1.5;
    color: #374151;
    margin-bottom: 8px;
    padding-left: 20px;
    position: relative;
    font-weight: 400;
}

.achievements li::before {
    content: "▸";
    color: #0ea5e9;
    font-weight: 700;
    position: absolute;
    left: 0;
    font-size: 14px;
    top: 1px;
}

/* Education Styles */
.education-item {
    margin-bottom: 16px;
    padding: 14px 16px;
    border-left: 4px solid #d1d5db;
    background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
    border-radius: 8px;
    page-break-inside: avoid;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.degree {
    font-size: 14px;
    font-weight: 700;
    color: #111827;
    margin-bottom: 4px;
    letter-spacing: -0.25px;
}

.institution {
    font-size: 13px;
    color: #374151;
    display: block;
    margin-bottom: 3px;
    font-weight: 400;
}

.year {
    font-size: 12px;
    color: #6b7280;
    font-weight: 600;
    font-style: italic;
}

/* Skills Grid */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 14px;
}

.skill-item {
    background: #fff5f5;
    border-left: 4px solid #f56565;
    border: 1px solid #fed7d7;
    padding: 10px 14px;
    border-radius: 8px;
    font-size: 13px;
    color: #0f172a;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(14, 165, 233, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.skill-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Personal Info */
.personal-info {
    font-size: 13px;
    line-height: 1.6;
    background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #4b5563;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.info-row {
    margin-bottom: 10px;
    color: #374151;
}

.info-row strong {
    color: #111827;
    font-weight: 600;
}

/* Print Styles - Optimized for A4 */
@media print {
    body {
        background: white !important;
        padding: 0 !important;
        margin: 0 !important;
        font-size: 12px !important;
        line-height: 1.4 !important;
        color: #1f2937 !important;
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    }

    .resume-container {
        box-shadow: none !important;
        max-width: none !important;
        margin: 0 !important;
        border-radius: 0 !important;
        overflow: visible !important;
        border: none !important;
        background: white !important;
    }

    .page {
        width: 100% !important;
        height: auto !important;
        min-height: auto !important;
        max-height: none !important;
        padding: 0 !important;
        margin: 0 !important;
        page-break-after: always !important;
        page-break-inside: avoid !important;
        box-shadow: none !important;
        overflow: visible !important;
        box-sizing: border-box !important;
        background: white !important;
        display: block !important;
    }

    .page:last-child {
        page-break-after: avoid !important;
    }

    .upload-btn, .print-btn, .download-btn {
        display: none !important;
    }

    /* Header - Optimized for A4 print */
    .header {
        background: #2d3748 !important;
        color: white !important;
        margin-bottom: 16px !important;
        padding: 20px 20px 16px 20px !important;
        box-shadow: none !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        justify-content: center !important;
        text-align: center !important;
        position: relative !important;
    }

    .header::after {
        content: '' !important;
        position: absolute !important;
        bottom: 0 !important;
        left: 0 !important;
        right: 0 !important;
        height: 3px !important;
        background: #f56565 !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    .profile-section {
        display: flex !important;
        flex-direction: column !important;
        align-items: center !important;
        margin-bottom: 16px !important;
    }

    .header-info {
        text-align: center !important;
    }

    .name {
        font-size: 22px !important;
        font-weight: 800 !important;
        color: white !important;
        margin-bottom: 8px !important;
        letter-spacing: -0.5px !important;
    }

    .title {
        font-size: 13px !important;
        font-weight: 600 !important;
        color: #f6ad55 !important;
        text-transform: uppercase !important;
        letter-spacing: 1px !important;
    }

    .contact-info {
        display: flex !important;
        flex-wrap: wrap !important;
        justify-content: center !important;
        gap: 8px !important;
        width: 100% !important;
    }

    .contact-item {
        display: flex !important;
        align-items: center !important;
        gap: 6px !important;
        font-size: 11px !important;
        color: white !important;
        font-weight: 500 !important;
        background: rgba(255, 255, 255, 0.1) !important;
        padding: 6px 12px !important;
        border-radius: 6px !important;
    }

    .contact-item i {
        color: #f56565 !important;
        font-size: 12px !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    /* Contact Links for Print */
    .contact-link {
        text-decoration: none !important;
        color: inherit !important;
    }

    .contact-link:hover {
        background: rgba(255, 255, 255, 0.1) !important;
        transform: none !important;
        box-shadow: none !important;
    }

    /* Sections - Optimized for A4 print */
    .section {
        margin-bottom: 14px !important;
        page-break-inside: avoid !important;
        padding: 0 20px !important;
    }

    .section-title {
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
        font-size: 14px !important;
        font-weight: 700 !important;
        color: #2d3748 !important;
        margin-bottom: 12px !important;
        text-transform: uppercase !important;
        letter-spacing: 0.6px !important;
        border-bottom: 2px solid #e5e7eb !important;
        padding-bottom: 6px !important;
        position: relative !important;
    }

    .section-title::after {
        content: '' !important;
        position: absolute !important;
        bottom: -2px !important;
        left: 0 !important;
        width: 40px !important;
        height: 2px !important;
        background: #f56565 !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    .section-title i {
        color: #f56565 !important;
        font-size: 14px !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    /* Professional Summary - Optimized for A4 print */
    .summary-text {
        font-size: 11px !important;
        line-height: 1.4 !important;
        color: #4a5568 !important;
        text-align: justify !important;
        font-weight: 400 !important;
        background: #fff5f5 !important;
        padding: 12px !important;
        border-radius: 6px !important;
        border-left: 3px solid #f56565 !important;
        margin-bottom: 12px !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    /* Experience Items - Optimized for A4 print */
    .experience-item {
        margin-bottom: 8px !important;
        padding: 10px !important;
        border-left: 3px solid #f56565 !important;
        background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%) !important;
        border-radius: 6px !important;
        page-break-inside: avoid !important;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06) !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    .experience-header {
        margin-bottom: 8px !important;
    }

    .job-title {
        font-size: 13px !important;
        font-weight: 700 !important;
        color: #111827 !important;
        margin-bottom: 3px !important;
        letter-spacing: -0.25px !important;
    }

    .company {
        font-size: 11px !important;
        color: #0ea5e9 !important;
        font-weight: 600 !important;
        display: block !important;
        margin-bottom: 2px !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    .duration {
        font-size: 10px !important;
        color: #6b7280 !important;
        font-weight: 500 !important;
        font-style: italic !important;
    }

    .achievements {
        list-style: none !important;
        padding-left: 0 !important;
        margin-top: 6px !important;
    }

    .achievements li {
        font-size: 10px !important;
        line-height: 1.2 !important;
        color: #374151 !important;
        margin-bottom: 3px !important;
        padding-left: 16px !important;
        position: relative !important;
        font-weight: 400 !important;
    }

    .achievements li::before {
        content: "▸" !important;
        color: #0ea5e9 !important;
        font-weight: 700 !important;
        position: absolute !important;
        left: 0 !important;
        font-size: 11px !important;
        top: 0px !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    /* Education Items - Optimized for A4 print */
    .education-item {
        margin-bottom: 10px !important;
        padding: 10px 12px !important;
        border-left: 3px solid #d1d5db !important;
        background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%) !important;
        border-radius: 6px !important;
        page-break-inside: avoid !important;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06) !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    .degree {
        font-size: 12px !important;
        font-weight: 700 !important;
        color: #111827 !important;
        margin-bottom: 3px !important;
        letter-spacing: -0.25px !important;
    }

    .institution {
        font-size: 10px !important;
        color: #374151 !important;
        display: block !important;
        margin-bottom: 2px !important;
        font-weight: 400 !important;
    }

    .year {
        font-size: 9px !important;
        color: #6b7280 !important;
        font-weight: 600 !important;
        font-style: italic !important;
    }

    /* Skills Grid - Optimized for A4 print */
    .skills-grid {
        display: grid !important;
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 8px !important;
    }

    .skill-item {
        background: #fff5f5 !important;
        border-left: 3px solid #f56565 !important;
        border: 1px solid #fed7d7 !important;
        padding: 6px 10px !important;
        border-radius: 6px !important;
        font-size: 10px !important;
        color: #0f172a !important;
        font-weight: 500 !important;
        box-shadow: 0 1px 2px rgba(14, 165, 233, 0.1) !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    .skill-item:hover {
        transform: none !important;
        box-shadow: 0 1px 2px rgba(14, 165, 233, 0.1) !important;
    }

    /* Personal Info - Optimized for A4 print */
    .personal-info {
        font-size: 10px !important;
        line-height: 1.4 !important;
        background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%) !important;
        padding: 12px !important;
        border-radius: 6px !important;
        border-left: 3px solid #4b5563 !important;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06) !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    .info-row {
        margin-bottom: 6px !important;
        color: #374151 !important;
    }

    .info-row strong {
        color: #111827 !important;
        font-weight: 600 !important;
    }

    /* Page Settings - A4 Optimized */
    @page {
        size: A4;
        margin: 0.4in 0.3in;
    }

    /* Force page breaks for better layout */
    .section {
        page-break-inside: avoid;
        break-inside: avoid;
    }

    .experience-item {
        page-break-inside: avoid;
        break-inside: avoid;
    }

    .education-item {
        page-break-inside: avoid;
        break-inside: avoid;
    }

    .skill-item {
        page-break-inside: avoid;
        break-inside: avoid;
    }

    /* Ensure page 1 content doesn't overflow */
    .page-1 {
        max-height: 10.5in !important;
        overflow: hidden !important;
    }

    /* Ensure page 2 starts fresh */
    .page-2 {
        page-break-before: always !important;
        margin-top: 0 !important;
        padding-top: 0 !important;
    }

    /* Hide interactive elements in print */
    .print-btn,
    .download-btn,
    .upload-btn {
        display: none !important;
    }

    /* Optimize text rendering for print */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }
}

/* Responsive Design */
@media screen and (max-width: 768px) {
    body {
        padding: 16px;
    }

    .resume-container {
        max-width: 100%;
        border-radius: 0;
        box-shadow: none;
    }

    .page {
        width: 100%;
        min-height: auto;
        padding: 24px;
    }

    .header {
        flex-direction: column;
        gap: 24px;
        margin-bottom: 32px;
    }

    .contact-info {
        min-width: auto;
        width: 100%;
    }

    .skills-grid {
        grid-template-columns: 1fr;
    }

    .name {
        font-size: 28px;
    }

    .profile-section {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .profile-image-container {
        width: 96px;
        height: 96px;
    }
}

