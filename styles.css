/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #1f2937;
    background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
    padding: 32px 0;
    font-weight: 400;
    font-size: 14px;
}

/* Resume Container */
.resume-container {
    max-width: 8.5in;
    margin: 0 auto;
    background: white;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid #e5e7eb;
}

/* Page Layout */
.page {
    width: 8.5in;
    min-height: 11in;
    background: white;
    page-break-after: always;
    page-break-inside: avoid;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}

.page:last-child {
    page-break-after: avoid;
}

/* Header Styles */
.header {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
    padding: 32px 32px 24px 32px;
    background: #2d3748;
    color: white;
    position: relative;
    text-align: center;
}

.header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: #f56565;
}

.profile-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 24px;
}

.header-info {
    text-align: center;
}

.name {
    font-size: 28px;
    font-weight: 800;
    color: white;
    margin-bottom: 12px;
    letter-spacing: -0.5px;
}

.title {
    font-size: 15px;
    font-weight: 600;
    color: #f6ad55;
    text-transform: uppercase;
    letter-spacing: 1.2px;
}

.contact-info {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 12px;
    width: 100%;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: white;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.1);
    padding: 8px 16px;
    border-radius: 8px;
}

.contact-item i {
    color: #f56565;
    font-size: 16px;
}

/* Section Styles */
.section {
    margin-bottom: 20px;
    page-break-inside: avoid;
    padding: 0 32px;
}

.section-title {
    display: flex;
    align-items: center;
    gap: 12px;
    font-size: 16px;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 16px;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 8px;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 60px;
    height: 2px;
    background: #f56565;
}

.section-title i {
    color: #f56565;
    font-size: 16px;
}

/* Professional Summary */
.summary-text {
    font-size: 14px;
    line-height: 1.6;
    color: #4a5568;
    text-align: justify;
    font-weight: 400;
    background: #fff5f5;
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #f56565;
}

/* Experience Styles */
.experience-item {
    margin-bottom: 18px;
    padding: 16px;
    border-left: 4px solid #f56565;
    background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
    border-radius: 8px;
    page-break-inside: avoid;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.experience-header {
    margin-bottom: 10px;
}

.job-title {
    font-size: 15px;
    font-weight: 700;
    color: #111827;
    margin-bottom: 4px;
    letter-spacing: -0.25px;
}

.company {
    font-size: 13px;
    color: #0ea5e9;
    font-weight: 600;
    display: block;
    margin-bottom: 3px;
}

.duration {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
    font-style: italic;
}

.achievements {
    list-style: none;
    padding-left: 0;
    margin-top: 8px;
}

.achievements li {
    font-size: 13px;
    line-height: 1.5;
    color: #374151;
    margin-bottom: 6px;
    padding-left: 20px;
    position: relative;
    font-weight: 400;
}

.achievements li::before {
    content: "▸";
    color: #0ea5e9;
    font-weight: 700;
    position: absolute;
    left: 0;
    font-size: 14px;
    top: 1px;
}

/* Education Styles */
.education-item {
    margin-bottom: 16px;
    padding: 14px 16px;
    border-left: 4px solid #d1d5db;
    background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
    border-radius: 8px;
    page-break-inside: avoid;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.degree {
    font-size: 14px;
    font-weight: 700;
    color: #111827;
    margin-bottom: 4px;
    letter-spacing: -0.25px;
}

.institution {
    font-size: 13px;
    color: #374151;
    display: block;
    margin-bottom: 3px;
    font-weight: 400;
}

.year {
    font-size: 12px;
    color: #6b7280;
    font-weight: 600;
    font-style: italic;
}

/* Skills Grid */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 14px;
}

.skill-item {
    background: #fff5f5;
    border-left: 4px solid #f56565;
    border: 1px solid #fed7d7;
    padding: 10px 14px;
    border-radius: 8px;
    font-size: 13px;
    color: #0f172a;
    font-weight: 500;
    box-shadow: 0 2px 4px rgba(14, 165, 233, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.skill-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Personal Info */
.personal-info {
    font-size: 13px;
    line-height: 1.6;
    background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
    padding: 16px;
    border-radius: 8px;
    border-left: 4px solid #4b5563;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.info-row {
    margin-bottom: 10px;
    color: #374151;
}

.info-row strong {
    color: #111827;
    font-weight: 600;
}

/* Print Styles */
@media print {
    body {
        background: white !important;
        padding: 0;
        margin: 0;
        font-size: 12px;
        line-height: 1.4;
    }

    .resume-container {
        box-shadow: none;
        max-width: none;
        margin: 0;
        border-radius: 0;
        overflow: visible;
    }

    .page {
        width: 100%;
        height: auto;
        min-height: 10.4in;
        padding: 0;
        margin: 0;
        page-break-after: always;
        page-break-inside: avoid;
        box-shadow: none;
        overflow: visible;
        box-sizing: border-box;
    }

    .page:nth-child(2) {
        padding-top: 20px;
    }

    .page:last-child {
        page-break-after: avoid;
    }

    .upload-btn {
        display: none !important;
    }

    /* Optimize header for print */
    .header {
        background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%) !important;
        color: white !important;
        margin-bottom: 18px !important;
        padding: 24px 24px 20px 24px !important;
        box-shadow: none !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    .header::after {
        height: 3px !important;
        background: linear-gradient(90deg, #0ea5e9 0%, #06b6d4 50%, #14b8a6 100%) !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    .name {
        color: white !important;
        font-size: 26px !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
        margin-bottom: 6px !important;
    }

    .title {
        color: #cbd5e1 !important;
        font-size: 14px !important;
    }

    .contact-info {
        gap: 10px !important;
        min-width: 280px !important;
    }

    .contact-item {
        color: white !important;
        font-size: 13px !important;
        gap: 12px !important;
    }

    .contact-item i {
        color: white !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
        font-size: 14px !important;
        width: 16px !important;
    }

    .profile-section {
        margin-right: 20px !important;
        gap: 20px !important;
    }

    .profile-image-container {
        width: 80px !important;
        height: 80px !important;
    }

    .profile-image {
        border: 3px solid white !important;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
    }

    /* Optimize sections for print */
    .section {
        padding: 0 24px !important;
        margin-bottom: 16px !important;
    }

    .section-title {
        font-size: 15px !important;
        margin-bottom: 14px !important;
        padding-bottom: 8px !important;
        border-bottom: 2px solid #e2e8f0 !important;
        gap: 10px !important;
    }

    .section-title::after {
        width: 40px !important;
        height: 2px !important;
        background: linear-gradient(90deg, #0ea5e9 0%, #06b6d4 100%) !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    .section-title i {
        color: #0ea5e9 !important;
        font-size: 15px !important;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    /* Optimize content styling for print */
    .summary-text {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
        padding: 14px !important;
        border-left: 4px solid #0ea5e9 !important;
        border-radius: 6px !important;
        font-size: 13px !important;
        line-height: 1.5 !important;
        margin-bottom: 16px !important;
        text-align: justify !important;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
        color: #334155 !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    .experience-item,
    .education-item {
        background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%) !important;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08) !important;
        border-left: 4px solid #d1d5db !important;
        padding: 12px 14px !important;
        margin-bottom: 14px !important;
        border-radius: 6px !important;
    }

    .job-title,
    .degree {
        font-size: 14px !important;
        margin-bottom: 4px !important;
        color: #111827 !important;
    }

    .company,
    .institution {
        font-size: 12px !important;
        margin-bottom: 3px !important;
        color: #0ea5e9 !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    .duration,
    .year {
        font-size: 11px !important;
        color: #6b7280 !important;
    }

    .experience-header {
        margin-bottom: 8px !important;
    }

    .achievements {
        margin-top: 8px !important;
    }

    .achievements li {
        font-size: 12px !important;
        line-height: 1.4 !important;
        margin-bottom: 4px !important;
        padding-left: 18px !important;
        color: #374151 !important;
    }

    .achievements li::before {
        color: #0ea5e9 !important;
        font-size: 13px !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    .skills-grid {
        gap: 10px !important;
    }

    .skill-item {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%) !important;
        border: 1px solid #bae6fd !important;
        border-left: 4px solid #0ea5e9 !important;
        padding: 8px 12px !important;
        font-size: 12px !important;
        box-shadow: 0 1px 3px rgba(14, 165, 233, 0.1) !important;
        border-radius: 6px !important;
        color: #0f172a !important;
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }

    .skill-item:hover {
        transform: none !important;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08) !important;
    }

    .personal-info {
        background: linear-gradient(135deg, #fefefe 0%, #f8fafc 100%) !important;
        padding: 14px !important;
        border-left: 4px solid #4299e1 !important;
        box-shadow: 0 1px 4px rgba(0, 0, 0, 0.08) !important;
        font-size: 12px !important;
        border-radius: 6px !important;
    }

    .info-row {
        margin-bottom: 10px !important;
        color: #475569 !important;
    }

    .info-row strong {
        color: #1e293b !important;
    }

    @page {
        size: 8.5in 11in;
        margin: 0.3in;
    }

    /* Ensure colors print correctly */
    .header {
        border-bottom: 2px solid #1a365d !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .profile-image {
        border: 3px solid #1a365d !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .section-title i,
    .contact-item i,
    .achievements li::before {
        color: #2b6cb0 !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    .skill-item {
        background: #f8fafc !important;
        border-left: 3px solid #2b6cb0 !important;
        border: 1px solid #e5e7eb !important;
        -webkit-print-color-adjust: exact;
        color-adjust: exact;
    }

    /* Force page breaks for better layout */
    .section {
        page-break-inside: avoid;
    }

    .experience-item {
        page-break-inside: avoid;
    }

    .education-item {
        page-break-inside: avoid;
    }

    /* Ensure proper spacing in print */
    .section {
        margin-bottom: 16px;
    }

    .experience-item {
        margin-bottom: 12px;
    }

    .achievements li {
        margin-bottom: 4px;
        font-size: 12px;
        line-height: 1.4;
    }
}

/* Responsive Design */
@media screen and (max-width: 768px) {
    body {
        padding: 16px;
    }

    .resume-container {
        max-width: 100%;
        border-radius: 0;
        box-shadow: none;
    }

    .page {
        width: 100%;
        min-height: auto;
        padding: 24px;
    }

    .header {
        flex-direction: column;
        gap: 24px;
        margin-bottom: 32px;
    }

    .contact-info {
        min-width: auto;
        width: 100%;
    }

    .skills-grid {
        grid-template-columns: 1fr;
    }

    .name {
        font-size: 28px;
    }

    .profile-section {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }

    .profile-image-container {
        width: 96px;
        height: 96px;
    }
}

