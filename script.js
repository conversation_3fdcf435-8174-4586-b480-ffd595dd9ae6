// Image Upload Functionality
document.addEventListener('DOMContentLoaded', function() {
    const imageUpload = document.getElementById('imageUpload');
    const profileImage = document.getElementById('profileImage');
    const uploadBtn = document.querySelector('.upload-btn');

    // Handle image upload
    imageUpload.addEventListener('change', function(event) {
        const file = event.target.files[0];

        if (file) {
            // Validate file type
            if (!file.type.startsWith('image/')) {
                alert('Please select a valid image file.');
                return;
            }

            // Validate file size (max 5MB)
            if (file.size > 5 * 1024 * 1024) {
                alert('Please select an image smaller than 5MB.');
                return;
            }

            const reader = new FileReader();

            reader.onload = function(e) {
                profileImage.src = e.target.result;

                // Store the image in localStorage for persistence
                try {
                    localStorage.setItem('resumeProfileImage', e.target.result);
                } catch (error) {
                    console.warn('Could not save image to localStorage:', error);
                }
            };

            reader.onerror = function() {
                alert('Error reading the image file. Please try again.');
            };

            reader.readAsDataURL(file);
        }
    });

    // Clear saved image on page refresh/load
    localStorage.removeItem('resumeProfileImage');

    // Reset to default placeholder image
    profileImage.src = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik01MCA1MEMzOC45NTQzIDUwIDMwIDQxLjA0NTcgMzAgMzBDMzAgMTguOTU0MyAzOC45NTQzIDEwIDUwIDEwQzYxLjA0NTcgMTAgNzAgMTguOTU0MyA3MCAzMEM3MCA0MS4wNDU3IDYxLjA0NTcgNTAgNTAgNTBaTTUwIDYwQzY2LjU2ODUgNjAgODAgNzMuNDMxNSA4MCA5MEgyMEM2MCA3My40MzE1IDMzLjQzMTUgNjAgNTAgNjBaIiBmaWxsPSIjOUM5Qzk5Ii8+Cjwvc3ZnPgo=";

    // Remove hover effects for static design

    // Print functionality
    function setupPrintButton() {
        // Create print button if it doesn't exist
        if (!document.querySelector('.print-btn')) {
            const printBtn = document.createElement('button');
            printBtn.className = 'print-btn';
            printBtn.innerHTML = '<i class="fas fa-print"></i> Print Resume';
            printBtn.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: #1e40af;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 4px;
                cursor: pointer;
                font-family: inherit;
                font-size: 14px;
                font-weight: 600;
                box-shadow: 0 2px 8px rgba(30, 64, 175, 0.2);
                z-index: 1000;
                display: flex;
                align-items: center;
                gap: 8px;
            `;

            printBtn.addEventListener('click', function() {
                window.print();
            });

            document.body.appendChild(printBtn);
        }
    }

    // Initialize print button
    setupPrintButton();

    // Download as PDF functionality (using browser's print to PDF)
    function setupDownloadButton() {
        if (!document.querySelector('.download-btn')) {
            const downloadBtn = document.createElement('button');
            downloadBtn.className = 'download-btn';
            downloadBtn.innerHTML = '<i class="fas fa-download"></i> Download PDF';
            downloadBtn.style.cssText = `
                position: fixed;
                top: 70px;
                right: 20px;
                background: #059669;
                color: white;
                border: none;
                padding: 12px 20px;
                border-radius: 4px;
                cursor: pointer;
                font-family: inherit;
                font-size: 14px;
                font-weight: 600;
                box-shadow: 0 2px 8px rgba(5, 150, 105, 0.2);
                z-index: 1000;
                display: flex;
                align-items: center;
                gap: 8px;
            `;

            downloadBtn.addEventListener('click', function() {
                // Show instructions for downloading as PDF
                const instructions = `
To download as PDF:
1. Click the Print button or press Ctrl+P (Cmd+P on Mac)
2. Select "Save as PDF" as the destination
3. Choose "More settings" and ensure:
   - Paper size: A4
   - Margins: Default
   - Scale: 100%
   - Background graphics: Checked
4. Click "Save"

This will create a perfect 2-page PDF copy of your resume!
                `;
                alert(instructions.trim());
            });

            document.body.appendChild(downloadBtn);
        }
    }

    // Initialize download button
    setupDownloadButton();

    // Hide action buttons when printing
    window.addEventListener('beforeprint', function() {
        const printBtn = document.querySelector('.print-btn');
        const downloadBtn = document.querySelector('.download-btn');
        if (printBtn) printBtn.style.display = 'none';
        if (downloadBtn) downloadBtn.style.display = 'none';
    });

    window.addEventListener('afterprint', function() {
        const printBtn = document.querySelector('.print-btn');
        const downloadBtn = document.querySelector('.download-btn');
        if (printBtn) printBtn.style.display = 'flex';
        if (downloadBtn) downloadBtn.style.display = 'flex';
    });

    // Add keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Ctrl+P or Cmd+P for print
        if ((e.ctrlKey || e.metaKey) && e.key === 'p') {
            e.preventDefault();
            window.print();
        }

        // Ctrl+U or Cmd+U for upload image
        if ((e.ctrlKey || e.metaKey) && e.key === 'u') {
            e.preventDefault();
            imageUpload.click();
        }
    });

    console.log('Resume application loaded successfully!');
    console.log('Keyboard shortcuts:');
    console.log('- Ctrl+P (Cmd+P): Print resume');
    console.log('- Ctrl+U (Cmd+U): Upload profile image');
});
